#!/bin/bash

# Build Single Executable Application (SEA) script
# Usage: ./build-sea.sh <node_version> <architecture> <js_script_path> [output_name]
# Example: ./build-sea.sh v20.19.4-linux-x64 ./dist/zyr.js zyr

set -euo pipefail

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 <node_version> <architecture> <js_script_path> [output_name]"
    echo ""
    echo "Arguments:"
    echo "  node_version    Node.js version (e.g., v20.19.4)"
    echo "  architecture    Target architecture (e.g., x64, arm64, armv7l)"
    echo "  js_script_path  Path to the JavaScript file to bundle"
    echo "  output_name     Optional output executable name (default: script basename)"
    echo ""
    echo "Examples:"
    echo "  $0 v20.19.4 x64 ./dist/zyr.js"
    echo "  $0 v20.19.4 arm64 ./dist/kinopoisk.js kinopoisk-arm64"
    echo ""
    echo "Supported architectures:"
    echo "  - x64 (Intel/AMD 64-bit)"
    echo "  - arm64 (Apple Silicon, ARM 64-bit)"
    echo "  - armv7l (ARM 32-bit)"
}

# Check arguments
if [ $# -lt 2 ] || [ $# -gt 3 ]; then
    log_error "Invalid number of arguments"
    show_usage
    exit 1
fi

NODE_FILENAME="node-$1"
JS_SCRIPT_PATH="$2"
OUTPUT_NAME="${3:-$(basename "$JS_SCRIPT_PATH" .js)}"

# Validate inputs
if [ ! -f "$JS_SCRIPT_PATH" ]; then
    log_error "JavaScript file not found: $JS_SCRIPT_PATH"
    exit 1
fi

# Create temporary directory
TEMP_DIR=$(mktemp -d)
trap 'rm -rf "$TEMP_DIR"' EXIT

log_info "Using temporary directory: $TEMP_DIR"

# Construct Node.js download URL and filename
    NODE_ARCHIVE="${NODE_FILENAME}.tar.gz"
NODE_BINARY="node"

NODE_URL="https://nodejs.org/dist/${NODE_VERSION}/${NODE_ARCHIVE}"

log_info "Downloading Node.js ${$1}..."
log_info "URL: $NODE_URL"

# Download Node.js
if ! curl -fsSL "$NODE_URL" -o "$TEMP_DIR/$NODE_ARCHIVE"; then
    log_error "Failed to download Node.js from $NODE_URL"
    exit 1
fi

log_success "Downloaded Node.js archive"

# Extract Node.js
log_info "Extracting Node.js..."
cd "$TEMP_DIR"
tar -xzf "$NODE_ARCHIVE"


NODE_BINARY_PATH="$TEMP_DIR/$NODE_FILENAME/bin/$NODE_BINARY"

if [ ! -f "$NODE_BINARY_PATH" ]; then
    log_error "Node.js binary not found at: $NODE_BINARY_PATH"
    exit 1
fi

log_success "Extracted Node.js binary"

# Check if postject is available
if ! command -v postject &> /dev/null; then
    log_warning "postject not found, installing globally..."
    if ! npm install -g postject; then
        log_error "Failed to install postject"
        exit 1
    fi
fi

# Create SEA configuration
SEA_CONFIG="$TEMP_DIR/sea-config.json"
cat > "$SEA_CONFIG" << EOF
{
  "main": "$(basename "$JS_SCRIPT_PATH")",
  "output": "sea-prep.blob",
  "disableExperimentalSEAWarning": true,
  "useSnapshot": false,
  "useCodeCache": true
}
EOF

log_info "Created SEA configuration"

# Copy the JavaScript file to temp directory
cp "$JS_SCRIPT_PATH" "$TEMP_DIR/"

log_info "Generating SEA blob..."

# Generate SEA blob using the downloaded Node.js binary
cd "$TEMP_DIR"
if ! "$NODE_BINARY_PATH" --experimental-sea-config "$SEA_CONFIG"; then
    log_error "Failed to generate SEA blob"
    exit 1
fi

if [ ! -f "$TEMP_DIR/sea-prep.blob" ]; then
    log_error "SEA blob not generated"
    exit 1
fi

log_success "Generated SEA blob"

# Copy Node.js binary for modification
EXECUTABLE_PATH="$TEMP_DIR/$OUTPUT_NAME"

cp "$NODE_BINARY_PATH" "$EXECUTABLE_PATH"

log_info "Injecting SEA blob into binary..."

# Inject SEA blob using postject
if ! postject "$EXECUTABLE_PATH" NODE_SEA_BLOB "$TEMP_DIR/sea-prep.blob" \
    --sentinel-fuse NODE_SEA_FUSE_fce680ab2cc467b6e072b8b5df1996b2 \
    --macho-segment-name NODE_SEA; then
    log_error "Failed to inject SEA blob"
    exit 1
fi

log_success "Injected SEA blob into binary"

# Move the final executable to the current directory
FINAL_EXECUTABLE="$(pwd)/$OUTPUT_NAME"

mv "$EXECUTABLE_PATH" "$FINAL_EXECUTABLE"

# Make executable
chmod +x "$FINAL_EXECUTABLE"

log_success "Single Executable Application created: $FINAL_EXECUTABLE"

# Show file info
if command -v file &> /dev/null; then
    log_info "File info:"
    file "$FINAL_EXECUTABLE"
fi

log_info "File size: $(du -h "$FINAL_EXECUTABLE" | cut -f1)"

log_success "Build completed successfully!"
echo ""
echo "You can now run your application with:"
echo "  $FINAL_EXECUTABLE"
